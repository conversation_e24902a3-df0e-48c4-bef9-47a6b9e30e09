"""
Punto de entrada principal para la aplicación FastAPI.
"""

import os
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path

from .routers import chat
from .utils.config import settings
# Comentamos la importación del MLService para evitar errores
# from .services.ml_service import MLService

# Crear la aplicación FastAPI
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir los routers
app.include_router(chat.router)

# Evento de inicio para inicializar los servicios
@app.on_event("startup")
async def startup_event():
    # Saltamos la inicialización del servicio ML
    # MLService.initialize()
    print("Saltando inicialización del servicio ML debido a problemas de dependencias")

    # Inicializar el servicio AI
    try:
        from .services.ai_service import AIService
        AIService.initialize()
    except Exception as e:
        print(f"Error al inicializar el servicio AI: {e}")

# Determinar la ruta al directorio frontend
frontend_dir = Path(__file__).resolve().parent.parent.parent / "frontend"

# Montar los archivos estáticos si el directorio frontend existe
if frontend_dir.exists():
    app.mount("/", StaticFiles(directory=frontend_dir, html=True), name="static")

@app.get("/", response_class=HTMLResponse, include_in_schema=False)
async def root(request: Request):
    """
    Ruta raíz que sirve la página principal.
    """
    # Si los archivos estáticos están montados, esta ruta no se ejecutará
    # porque la ruta estática tendrá prioridad.
    # Esta es solo una copia de seguridad en caso de que los archivos estáticos no estén montados.
    html_file = frontend_dir / "index.html"
    if html_file.exists():
        with open(html_file, "r", encoding="utf-8") as f:
            return f.read()
    else:
        return """
        <html>
            <head>
                <title>Asistente de Prácticas Profesionales UABC</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        background-color: #f5f5f5;
                    }
                    .container {
                        text-align: center;
                        padding: 20px;
                        background-color: white;
                        border-radius: 10px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        max-width: 600px;
                    }
                    h1 {
                        color: #006633;
                    }
                    p {
                        color: #333;
                    }
                    a {
                        color: #003366;
                        text-decoration: none;
                    }
                    a:hover {
                        text-decoration: underline;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Asistente de Prácticas Profesionales UABC</h1>
                    <p>La API está funcionando correctamente.</p>
                    <p>Accede a la <a href="/docs">documentación de la API</a>.</p>
                </div>
            </body>
        </html>
        """

@app.get("/health", tags=["health"])
async def health_check():
    """
    Endpoint para verificar el estado de la aplicación.
    """
    return {"status": "ok", "version": settings.APP_VERSION}
