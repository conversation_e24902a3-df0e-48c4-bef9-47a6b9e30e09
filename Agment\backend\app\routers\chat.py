"""
Router para el endpoint de chat.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from ..services.ai_service import AIService
from ..services.rule_service import RuleService
# Comentamos la importación del MLService para evitar errores
# from ..services.ml_service import MLService

# Crear el router
router = APIRouter(
    prefix="/api",
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)

# Modelo de datos para la solicitud
class ChatRequest(BaseModel):
    question: str
    mode: str = "ai"  # Valores posibles: "ai", "rule" o "ml"

# Modelo de datos para la respuesta
class ChatResponse(BaseModel):
    response: str

@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Endpoint para procesar preguntas y generar respuestas.

    Args:
        request: La solicitud que contiene la pregunta y el modo.

    Returns:
        Una respuesta con el texto generado.

    Raises:
        HTTPException: Si ocurre un error al procesar la solicitud.
    """
    try:
        # Validar el modo
        if request.mode not in ["ai", "rule", "ml"]:
            raise HTTPException(status_code=400, detail="Modo no válido. Debe ser 'ai', 'rule' o 'ml'.")

        # Generar la respuesta según el modo
        if request.mode == "ai":
            response_text = await AIService.get_response(request.question)
        elif request.mode == "ml":
            # Informamos que el servicio ML no está disponible
            response_text = "Lo siento, el servicio de Machine Learning no está disponible en este momento debido a problemas con las dependencias. Por favor, utiliza el modo AI o Rule."
        else:
            response_text = await RuleService.get_response(request.question)

        # Devolver la respuesta
        return ChatResponse(response=response_text)

    except Exception as e:
        # Capturar cualquier excepción y devolver un error
        raise HTTPException(status_code=500, detail=f"Error al procesar la solicitud: {str(e)}")
