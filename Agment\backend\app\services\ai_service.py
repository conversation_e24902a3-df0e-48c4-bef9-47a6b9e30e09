"""
Servicio para generar respuestas utilizando OpenRouter con datos de la base de datos.
"""

import json
import httpx
import pandas as pd
import os
from pathlib import Path
from ..utils.config import settings

class AIService:
    """
    Clase que implementa el servicio de respuestas basadas en IA.
    """

    # Variables para la base de datos
    db_data = None
    initialized = False

    @classmethod
    def initialize(cls):
        """
        Inicializa la base de datos para el servicio de IA.
        """
        if cls.initialized:
            return

        try:
            # Ruta al archivo de datos
            archivo = Path(__file__).resolve().parent.parent.parent / 'Base.xlsx'

            if not os.path.exists(archivo):
                print(f"Advertencia: El archivo {archivo} no existe para el servicio de IA.")
                cls.db_data = None
                return

            # Cargar datos
            data = pd.read_excel(archivo)

            # Verificar que el archivo tenga las columnas necesarias
            if 'Pregunta' not in data.columns or 'Respuesta' not in data.columns:
                print("Advertencia: El archivo no tiene las columnas 'Pregunta' y 'Respuesta' para el servicio de IA.")
                cls.db_data = None
                return

            # Guardar los datos
            cls.db_data = data
            cls.initialized = True
            print("Base de datos inicializada correctamente para el servicio de IA.")

        except Exception as e:
            print(f"Error al inicializar la base de datos para el servicio de IA: {str(e)}")
            cls.db_data = None

    @classmethod
    async def get_response(cls, question: str) -> str:
        """
        Genera una respuesta utilizando OpenRouter.

        Args:
            question: La pregunta del usuario.

        Returns:
            Una respuesta generada por el modelo de IA.
        """
        try:
            # Inicializar la base de datos si no se ha hecho
            if not cls.initialized:
                cls.initialize()

            # Verificar que la clave API esté configurada
            if not settings.OPENROUTER_API_KEY:
                return "Error: No se ha configurado la clave API de OpenRouter. Por favor, configura la variable de entorno OPENROUTER_API_KEY."

            # Preparar los datos para la solicitud
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                "HTTP-Referer": "http://localhost:8000",  # Requerido por OpenRouter
                "X-Title": "Asistente de Prácticas Profesionales UABC"  # Opcional, pero recomendado
            }

            # Buscar información relevante en la base de datos
            relevant_info = ""
            if cls.db_data is not None:
                # Convertir la pregunta a minúsculas para facilitar la comparación
                question_lower = question.lower()

                # Buscar preguntas similares en la base de datos
                for idx, row in cls.db_data.iterrows():
                    db_question = row['Pregunta'].lower()

                    # Verificar si hay palabras clave comunes
                    keywords = question_lower.split()
                    if any(keyword in db_question for keyword in keywords if len(keyword) > 3):
                        relevant_info += f"Pregunta similar: {row['Pregunta']}\n"
                        relevant_info += f"Respuesta: {row['Respuesta']}\n\n"

                if relevant_info:
                    relevant_info = "Información relevante de la base de datos:\n" + relevant_info

            # Crear el mensaje del sistema con contexto sobre prácticas profesionales
            system_message = f"""
            Eres un asistente virtual especializado en prácticas profesionales de la Universidad Autónoma de Baja California (UABC).
            Tu objetivo es proporcionar información precisa y útil a los estudiantes sobre el proceso de prácticas profesionales.

            Información importante sobre prácticas profesionales en la UABC:
            - Los estudiantes pueden iniciar sus prácticas al completar el 70% de los créditos de su carrera.
            - Se requieren 480 horas de prácticas profesionales.
            - Los documentos necesarios incluyen: carta de presentación, constancia de créditos, seguro facultativo y formato de registro.
            - La evaluación se realiza mediante reportes mensuales, evaluación del supervisor y reporte final.
            - La Coordinación de Prácticas Profesionales se encuentra en el edificio A, planta baja.

            {relevant_info}

            Utiliza la información de la base de datos si es relevante para la pregunta del usuario.
            Responde de manera clara, concisa y amigable. Si no conoces la respuesta a una pregunta específica,
            sugiere que el estudiante visite la Coordinación de Prácticas Profesionales para obtener información más detallada.
            """

            # Preparar el payload para la solicitud
            payload = {
                "model": settings.AI_MODEL,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": question}
                ]
            }

            # Realizar la solicitud a OpenRouter
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    settings.OPENROUTER_URL,
                    headers=headers,
                    json=payload
                )

                # Verificar si la solicitud fue exitosa
                if response.status_code == 200:
                    response_data = response.json()
                    # Extraer la respuesta del modelo
                    ai_response = response_data["choices"][0]["message"]["content"]
                    return ai_response
                else:
                    # Si hay un error, devolver un mensaje de error
                    error_message = f"Error al comunicarse con OpenRouter: {response.status_code} - {response.text}"
                    print(error_message)
                    return "Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, intenta de nuevo más tarde."

        except Exception as e:
            # Capturar cualquier excepción y devolver un mensaje de error
            error_message = f"Error en el servicio de IA: {str(e)}"
            print(error_message)
            return "Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, intenta de nuevo más tarde."
